allprojects {
    repositories {
        google()
        mavenCentral()
        // 添加个推仓库
        maven {
            url = uri("https://mvn.getui.com/nexus/content/repositories/releases/")
        }
        maven {
            url = uri("https://developer.huawei.com/repo/")
        }
        maven {
            url = uri("https://developer.hihonor.com/repo/")
        }
    }
}

//AAPT: error: resource android:attr/lStar not found
//https://github.com/flutter/flutter/issues/153281 flutter sdk升级至3.24.0问题
rootProject.buildDir = File("../build")

subprojects {
    afterEvaluate {
        if (plugins.hasPlugin("com.android.library") || plugins.hasPlugin("com.android.application")) {
            extensions.configure<com.android.build.gradle.BaseExtension>("android") {
//                compileSdkVersion(35)
            }
        }
    }
}

subprojects {
    buildDir = File("${rootProject.buildDir}/${name}")
    evaluationDependsOn(":app")
}


val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
