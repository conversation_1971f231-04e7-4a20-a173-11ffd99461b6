import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.huawei.agconnect")
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.sq.dlyz_flutter"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "com.sy.dlyz"
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        manifestPlaceholders += mapOf(
            "GETUI_APPID" to "Rti8eljkzx9ojQNDbAlJp3",

            // 华为 相关应用参数
            "HUAWEI_APP_ID" to "114983571",

            // 小米相关应用参数
            "XIAOMI_APP_ID"  to "",
            "XIAOMI_APP_KEY" to "",

            // OPPO 相关应用参数
            "OPPO_APP_KEY" to "beda7f2f42a64714bdf47b416fcfb3e6",
            "OPPO_APP_SECRET" to "03fd9e24d40b4b4097fe0c09e48f58a5",

            // VIVO 相关应用参数
            "VIVO_APP_ID" to "105951955",
            "VIVO_APP_KEY" to "3b9c6aaee024538ce33154e5ae7d899c",

            // 魅族相关应用参数
            "MEIZU_APP_ID" to "",
            "MEIZU_APP_KEY" to "",

            // 荣耀相关应用参数
            "HONOR_APP_ID" to "104516704"
        )
    }

    /**
     * 签名设置
     */
    signingConfigs {
        create("release") {
            val propFile = file("signing.properties")
            if (propFile.exists()) {
                val props = Properties()
                props.load(FileInputStream(propFile))
                if (props.containsKey("STORE_FILE") && props.containsKey("STORE_PASSWORD") &&
                    props.containsKey("KEY_ALIAS") && props.containsKey("KEY_PASSWORD")) {
                    storeFile = file(props["STORE_FILE"] as String)
                    storePassword = props["STORE_PASSWORD"] as String
                    keyAlias = props["KEY_ALIAS"] as String
                    keyPassword = props["KEY_PASSWORD"] as String
                }
            }
        }
        
        create("customDebug") {
            // debug版本也使用相同的签名配置，但使用不同的名称
            val propFile = file("signing.properties")
            if (propFile.exists()) {
                val props = Properties()
                props.load(FileInputStream(propFile))
                if (props.containsKey("STORE_FILE") && props.containsKey("STORE_PASSWORD") &&
                    props.containsKey("KEY_ALIAS") && props.containsKey("KEY_PASSWORD")) {
                    storeFile = file(props["STORE_FILE"] as String)
                    storePassword = props["STORE_PASSWORD"] as String
                    keyAlias = props["KEY_ALIAS"] as String
                    keyPassword = props["KEY_PASSWORD"] as String
                }
            }
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            isShrinkResources = false // Kotlin DSL 正确写法
        }
        debug {
            signingConfig = signingConfigs.getByName("customDebug") // 使用自定义debug签名
            isMinifyEnabled = false
        }
    }
}

dependencies {
    compileOnly("androidx.annotation:annotation:1.6.0")
    implementation("androidx.core:core-ktx:1.13.1")
    implementation("androidx.work:work-runtime:2.9.0")
    implementation("androidx.recyclerview:recyclerview:1.3.2")
    implementation("androidx.fragment:fragment-ktx:1.6.2")
    implementation("com.google.android.material:material:1.11.0")
    
    // Glide 图片加载库
    implementation("com.github.bumptech.glide:glide:4.16.0")
    
    // 阿里云闪验SDK依赖
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

    //支付宝支付sdk
    implementation("com.alipay.sdk:alipaysdk-android:+@aar")

    //------ 个推SDK start ------
    implementation("com.getui:gtsdk:********")
    //个推核心组件
    implementation("com.getui:gtc:********")
    // 根据所需厂商选择集成
    // 华为
    implementation("com.getui.opt:hwp:3.1.2")
    implementation("com.huawei.hms:push:6.12.0.300")
    // 小米
    implementation("com.getui.opt:xmp:3.3.3")
    // oppo
    implementation("com.assist-v3:oppo:3.5.0")
    implementation("com.google.code.gson:gson:2.6.2")
    implementation("commons-codec:commons-codec:1.6")
    implementation("com.android.support:support-annotations:28.0.0")
    // vivo
    implementation("com.assist-v3:vivo:3.2.0")
    // 魅族
    implementation("com.getui.opt:mzp:3.3.0")
    // ups，ups目前支持坚果，索尼，海信手机
    implementation("com.getui.opt:ups:3.0.3")
    // 荣耀
    implementation("com.getui.opt:honor:3.6.0")
    implementation("com.hihonor.mcs:push:7.0.61.303")
    //------ 个推SDK end ------
}

flutter {
    source = "../.."
}
