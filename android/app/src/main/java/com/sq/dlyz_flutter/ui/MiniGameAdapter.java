package com.sq.dlyz_flutter.ui;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.sq.dlyz_flutter.R;
import java.util.List;

public class MiniGameAdapter extends RecyclerView.Adapter<MiniGameAdapter.ViewHolder> {
    private final List<MiniGameOption> gameOptions;
    private final Context context;
    private OnGameClickListener onGameClickListener;

    public interface OnGameClickListener {
        void onGameClick(MiniGameOption option);
    }

    public MiniGameAdapter(Context context, List<MiniGameOption> gameOptions) {
        this.context = context;
        this.gameOptions = gameOptions;
    }

    public void setOnGameClickListener(OnGameClickListener listener) {
        this.onGameClickListener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_mini_game, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        MiniGameOption option = gameOptions.get(position);
        
        holder.tvGameName.setText(option.getTitle());
        
        // 使用Glide加载网络图片
        if (option.getImageUrl() != null && !option.getImageUrl().isEmpty()) {
            Glide.with(context)
                    .load(option.getImageUrl())
                    .placeholder(R.drawable.game_icon_placeholder)
                    .error(R.drawable.game_icon_placeholder)
                    .circleCrop()
                    .transition(DrawableTransitionOptions.withCrossFade())
                    .into(holder.ivGameIcon);
        } else {
            holder.ivGameIcon.setImageResource(R.drawable.game_icon_placeholder);
        }
        
        holder.itemView.setOnClickListener(v -> {
            if (onGameClickListener != null) {
                onGameClickListener.onGameClick(option);
            }
        });
    }

    @Override
    public int getItemCount() {
        return gameOptions.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivGameIcon;
        TextView tvGameName;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivGameIcon = itemView.findViewById(R.id.iv_game_icon);
            tvGameName = itemView.findViewById(R.id.tv_game_name);
        }
    }
}