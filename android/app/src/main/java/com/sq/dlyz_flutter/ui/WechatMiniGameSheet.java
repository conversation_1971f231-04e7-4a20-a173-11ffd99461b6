package com.sq.dlyz_flutter.ui;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.sq.dlyz_flutter.R;
import java.util.ArrayList;
import java.util.List;

public class WechatMiniGameSheet implements MiniGameAdapter.OnGameClickListener {
    private static final String TAG = "WechatMiniGameSheet";
    private final List<MiniGameOption> gameOptions;
    private OnGameSelectedListener onGameSelectedListener;
    private BottomSheetDialog bottomSheetDialog;

    public interface OnGameSelectedListener {
        void onGameSelected(MiniGameOption option);
    }

    public WechatMiniGameSheet(List<MiniGameOption> options) {
        this.gameOptions = options != null ? options : new ArrayList<>();
    }

    public void setOnGameSelectedListener(OnGameSelectedListener listener) {
        this.onGameSelectedListener = listener;
    }

    @Override
    public void onGameClick(MiniGameOption option) {
        Log.d(TAG, "Game selected: " + option.getTitle());
        
        if (onGameSelectedListener != null) {
            onGameSelectedListener.onGameSelected(option);
        }
        
        // 关闭弹窗
        if (bottomSheetDialog != null && bottomSheetDialog.isShowing()) {
            bottomSheetDialog.dismiss();
        }
    }

    public void show(Context context) {
        try {
            // 创建BottomSheetDialog
            bottomSheetDialog = new BottomSheetDialog(context);
            bottomSheetDialog.setOnShowListener((dialog) -> {
                View bottomSheet = ((BottomSheetDialog)dialog).findViewById(com.google.android.material.R.id.design_bottom_sheet);
                if (bottomSheet != null) {
                    bottomSheet.setBackground(ContextCompat.getDrawable(context, R.drawable.wechat_mini_game_sheet_bg));
                }
            });
            // 创建自定义布局
            View contentView = LayoutInflater.from(context).inflate(R.layout.dialog_wechat_mini_game_sheet, null);
            
            // 设置RecyclerView
            RecyclerView recyclerView = contentView.findViewById(R.id.rv_mini_games);
            int spanCount = gameOptions.size() >=3 ? 3 : 2;
            GridLayoutManager gridLayoutManager = new GridLayoutManager(context, spanCount);
            recyclerView.setLayoutManager(gridLayoutManager);
            
            MiniGameAdapter adapter = new MiniGameAdapter(context, gameOptions);
            adapter.setOnGameClickListener(this);
            recyclerView.setAdapter(adapter);
            
            // 设置内容视图
            bottomSheetDialog.setContentView(contentView);
            
            // 设置BottomSheetDialog背景为透明，让自定义圆角显示

            
            // 显示弹窗
            bottomSheetDialog.show();
            
            Log.d(TAG, "WechatMiniGameSheet shown with " + gameOptions.size() + " games");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to show dialog: " + e.getMessage());
        }
    }

    public static void show(Context context, List<MiniGameOption> options, OnGameSelectedListener listener) {
        try {
            WechatMiniGameSheet sheet = new WechatMiniGameSheet(options);
            sheet.setOnGameSelectedListener(listener);
            sheet.show(context);
        } catch (Exception e) {
            Log.e(TAG, "Failed to create and show dialog: " + e.getMessage());
        }
    }
}