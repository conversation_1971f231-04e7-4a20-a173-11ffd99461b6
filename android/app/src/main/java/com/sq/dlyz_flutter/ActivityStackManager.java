package com.sq.dlyz_flutter;

import android.app.Activity;
import android.util.Log;

import java.lang.ref.WeakReference;
import java.util.Stack;

/**
 * Activity栈管理器，用于管理应用中的Activity生命周期
 */
public class ActivityStackManager {
    private static final String TAG = "ActivityStackManager";
    private static volatile ActivityStackManager instance;
    private Stack<WeakReference<Activity>> activityStack;

    private ActivityStackManager() {
        activityStack = new Stack<>();
    }

    /**
     * 获取单例实例
     */
    public static ActivityStackManager getInstance() {
        if (instance == null) {
            synchronized (ActivityStackManager.class) {
                if (instance == null) {
                    instance = new ActivityStackManager();
                }
            }
        }
        return instance;
    }

    /**
     * 添加Activity到栈顶
     */
    public void addActivity(Activity activity) {
        if (activity != null) {
            // 清理已销毁的Activity引用
            cleanDestroyedActivities();
            
            activityStack.push(new WeakReference<>(activity));
            Log.d(TAG, "Activity added to stack: " + activity.getClass().getSimpleName() + ", stack size: " + activityStack.size());
        }
    }

    /**
     * 从栈中移除Activity
     */
    public void removeActivity(Activity activity) {
        if (activity != null) {
            for (int i = activityStack.size() - 1; i >= 0; i--) {
                WeakReference<Activity> ref = activityStack.get(i);
                Activity stackActivity = ref.get();
                if (stackActivity == null || stackActivity == activity) {
                    activityStack.remove(i);
                    Log.d(TAG, "Activity removed from stack: " + activity.getClass().getSimpleName() + ", stack size: " + activityStack.size());
                }
            }
        }
    }

    /**
     * 获取栈顶Activity（当前显示的Activity）
     */
    public Activity getTopActivity() {
        // 清理已销毁的Activity引用
        cleanDestroyedActivities();
        
        if (!activityStack.isEmpty()) {
            WeakReference<Activity> topRef = activityStack.peek();
            Activity topActivity = topRef.get();
            
            if (topActivity != null && !topActivity.isFinishing() && !topActivity.isDestroyed()) {
                Log.d(TAG, "Top activity: " + topActivity.getClass().getSimpleName());
                return topActivity;
            } else {
                // 栈顶Activity已销毁，移除并递归查找
                activityStack.pop();
                return getTopActivity();
            }
        }
        
        Log.w(TAG, "No valid top activity found, stack is empty or all activities are destroyed");
        return null;
    }

    /**
     * 清理已销毁的Activity引用
     */
    private void cleanDestroyedActivities() {
        for (int i = activityStack.size() - 1; i >= 0; i--) {
            WeakReference<Activity> ref = activityStack.get(i);
            Activity activity = ref.get();
            if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                activityStack.remove(i);
            }
        }
    }

    /**
     * 获取当前栈大小
     */
    public int getStackSize() {
        cleanDestroyedActivities();
        return activityStack.size();
    }

    /**
     * 清空Activity栈
     */
    public void clearStack() {
        activityStack.clear();
        Log.d(TAG, "Activity stack cleared");
    }
}