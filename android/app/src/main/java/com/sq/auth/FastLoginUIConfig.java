package com.sq.auth;
import android.content.pm.ActivityInfo;
import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.Gravity;
import android.widget.LinearLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.mobile.auth.gatewayauth.AuthRegisterXmlConfig;
import com.mobile.auth.gatewayauth.AuthUIConfig;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.mobile.auth.gatewayauth.AuthRegisterViewConfig;
import com.mobile.auth.gatewayauth.ui.AbstractPnsViewDelegate;
import com.sq.dlyz_flutter.R;
import com.sq.dlyz_flutter.DouluoManagerChannel;

/**
 * 一键登录UI配置管理器
 * 提供自定义的UI样式配置和交互处理
 * 重构为全屏页面模式，提升用户体验
 */
public class FastLoginUIConfig {

    private static final String TAG = "FastLoginUIConfig";

    /**
     * 创建一键登录UI配置
     * 全屏页面模式配置，支持导航栏和自定义布局
     */
    public static AuthUIConfig createCustomUIConfig(Context context) {
        int unit = 5;

        // 从FastLoginManager获取协议地址
        FastLoginManager manager = FastLoginManager.getInstance(context);
        String userAgreementUrl = manager.getUserAgreementUrl();
        String privacyPolicyUrl = manager.getPrivacyPolicyUrl();

        return new AuthUIConfig.Builder().setAppPrivacyOne("《用户协议》", userAgreementUrl)
            .setAppPrivacyTwo("《隐私政策》", privacyPolicyUrl)
            .setAppPrivacyColor(Color.GRAY, Color.parseColor("#4571FB")).setPrivacyConectTexts(new String[]{"及"})
            .setPrivacyTextSizeDp(12)

            //设置运营商协议显示位置
            .setPrivacyOperatorIndex(2).setPrivacyState(false).setPrivacyOffsetY_B(20)
            .setStatusBarColor(Color.TRANSPARENT).setStatusBarUIFlag(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
            .setNavHidden(true)                     // 隐藏导航栏，为轮播图腾出空间

            .setPageBackgroundPath("wechat_mini_game_sheet_bg")  // 页面背景为白色
            .setLogoHidden(true)                    // 隐藏logo
            .setSloganHidden(false)
            .setSloganTextSizeDp(12)
            .setSloganOffsetY(270)// 隐藏slogan

            // === 手机号码显示配置 ===
            .setNumFieldOffsetY(230)                // 手机号距离顶部位置
            .setNumberSizeDp(33)                    // 手机号文字大小
            .setNumberColor(Color.parseColor("#FF212121"))  // 手机号文字颜色

            // === 登录按钮配置 ===             // 按钮宽度（全屏模式下稍微增大）
            .setLogBtnHeight(50)                    // 按钮高度
            .setLogBtnMarginLeftAndRight(16)        // 按钮左右边距
            .setLogBtnTextSizeDp(17)                // 按钮文字大小
            .setLogBtnText("本机号码一键登录")        // 按钮文字
            .setLogBtnTextColor(Color.WHITE)        // 按钮文字颜色
            .setLogBtnBackgroundPath("sysq_dialog_login_btn_bg")  // 按钮背景
            .setLogBtnOffsetY(320)                  // 按钮距离顶部位置
            .setSwitchAccHidden(true)
            // === 隐私协议配置 ===
            .setVendorPrivacyPrefix("《").setVendorPrivacySuffix("》").setCheckboxHidden(false)               // 显示复选框
            .setCheckedImgDrawable(context.getResources().getDrawable(R.drawable.sysq_dialog_login_privacy_check_box))

            // === 动画配置 ===
            .setAuthPageActIn("in_activity", "out_activity")   // 进入动画
            .setAuthPageActOut("in_activity", "out_activity")  // 退出动画

            .setScreenOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)  // 竖屏显示
            .setLogBtnToastHidden(true)             // 隐藏Toast提示
            .setProtocolAction(context.getPackageName() + ".protocolWeb")  // 协议页面Action
            .create();
    }

    /**
     * dp转px工具方法
     */
    private static int dip2px(Context context, float dpValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue,
            context.getResources().getDisplayMetrics());
    }

    /**
     * 从 Flutter 资源中加载 Bitmap
     */
    private static Bitmap loadFlutterAssetBitmap(Context context, String assetName) {
        try {
            AssetManager am = context.getAssets();
            // Flutter 打包后资源前缀为 flutter_assets/
            String fullPath = "flutter_assets/assets/images/" + assetName;
            return BitmapFactory.decodeStream(am.open(fullPath));
        } catch (Exception e) {
            Log.e(TAG, "加载Flutter资源失败: " + assetName, e);
            return null;
        }
    }


    /**
     * 底部三按钮区域：微信登录、账号密码登录、其他手机号登录
     * 如果授权应用列表为空，则不显示微信登录按钮
     */
    private static View initBottomActionBar(Context context, DouluoManagerChannel channel, boolean hasAuthApps) {
        LinearLayout bar = new LinearLayout(context);
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT);
        lp.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
        lp.setMargins(0, 0, 0, dip2px(context, 80));
        bar.setLayoutParams(lp);
        bar.setOrientation(LinearLayout.HORIZONTAL);

        // 仅在有授权应用时添加微信登录按钮
        if (hasAuthApps) {
            View wechatItem = createLoginItem(context, "微信登录", "login_wechat.png", new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (channel != null) {
                        channel.onFastLoginWeChatClicked();
                    }
                }
            });
            bar.addView(wechatItem);
            Log.d(TAG, "已添加微信登录按钮");
        } else {
            Log.d(TAG, "授权应用列表为空，不显示微信登录按钮");
        }

        // 添加账号密码登录按钮
        View accountItem = createLoginItem(context, "账号密码登录", "login_account.png", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (channel != null) {
                    channel.onFastLoginAccountLoginClicked();
                }
            }
        });
        bar.addView(accountItem);

        return bar;
    }

    private static View createLoginItem(Context context, String label, String assetName, View.OnClickListener onClick) {
        LinearLayout item = new LinearLayout(context);
        LinearLayout.LayoutParams ilp = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT);
        ilp.setMargins(dip2px(context, 20), 0, dip2px(context, 20), 0);
        item.setLayoutParams(ilp);
        item.setOrientation(LinearLayout.VERTICAL);
        item.setGravity(Gravity.CENTER);

        ImageView icon = new ImageView(context);
        LinearLayout.LayoutParams imgLp = new LinearLayout.LayoutParams(dip2px(context, 56), dip2px(context, 56));
        icon.setLayoutParams(imgLp);
        Bitmap bmp = loadFlutterAssetBitmap(context, assetName);
        if (bmp != null) {
            icon.setImageBitmap(bmp);
        }

        TextView text = new TextView(context);
        LinearLayout.LayoutParams tlp = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT);
        tlp.setMargins(0, dip2px(context, 6), 0, 0);
        text.setLayoutParams(tlp);
        text.setText(label);
        text.setTextColor(Color.parseColor("#666666"));
        text.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f);

        item.setOnClickListener(onClick);
        item.addView(icon);
        item.addView(text);
        return item;
    }

    /**
     * 一键登录下方的“其他手机号登录”按钮（描边）
     */
    private static View initOtherPhoneButton(Context context, DouluoManagerChannel channel) {
        TextView btn = new TextView(context);
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(LayoutParams.MATCH_PARENT
            , dip2px(context, 50));
        lp.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        lp.setMargins(dip2px(context, 16), dip2px(context, 390), dip2px(context, 16), 0); // 位于一键登录按钮下方
        btn.setLayoutParams(lp);
        btn.setText("其他手机号登录");
        btn.setGravity(Gravity.CENTER);
        btn.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f);
        btn.setTextColor(Color.parseColor("#303133"));

        GradientDrawable border = new GradientDrawable();
        border.setColor(Color.TRANSPARENT);
        border.setCornerRadius(dip2px(context, 8));
        border.setStroke(dip2px(context, 1), Color.parseColor("#ff303133"));
        btn.setBackground(border);

        btn.setOnClickListener(v -> {
            if (channel != null) {
                channel.onFastLoginOtherPhoneClicked();
            }
        });
        return btn;
    }

    /**
     * 配置一键登录界面样式
     * 设置轮播图、自定义视图配置
     */
    public static void configAuthPage(PhoneNumberAuthHelper authHelper, Context context, DouluoManagerChannel channel, boolean hasAuthApps) {
        try {
            // 清理之前的配置
            authHelper.removeAuthRegisterXmlConfig();
            authHelper.removeAuthRegisterViewConfig();

            authHelper.addAuthRegisterXmlConfig(
                new AuthRegisterXmlConfig.Builder().setLayout(R.layout.layout_auth_top, new AbstractPnsViewDelegate() {
                    @Override
                    public void onViewCreated(View view) {

                    }
                }).build());

            // 底部按钮（微信/账号密码）- 根据授权应用列表决定是否显示微信按钮
            authHelper.addAuthRegistViewConfig("bottom_actions",
                new AuthRegisterViewConfig.Builder().setView(initBottomActionBar(context, channel, hasAuthApps))
                    .setRootViewId(AuthRegisterViewConfig.RootViewId.ROOT_VIEW_ID_BODY).build());

            // 一键登录下方的"其他手机号登录"
            authHelper.addAuthRegistViewConfig("other_phone_btn",
                new AuthRegisterViewConfig.Builder().setView(initOtherPhoneButton(context, channel))
                    .setRootViewId(AuthRegisterViewConfig.RootViewId.ROOT_VIEW_ID_BODY).build());
            Log.d(TAG, "自定义按钮组配置成功，hasAuthApps=" + hasAuthApps);

            // 设置基础AuthUIConfig
            AuthUIConfig uiConfig = createCustomUIConfig(context);
            authHelper.setAuthUIConfig(uiConfig);

            Log.d(TAG, "闪验页面配置完成（包含轮播图）");
        } catch (Exception e) {
            Log.e(TAG, "配置一键登录UI失败", e);
        }
    }
}