package com.sq.auth;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;
import com.mobile.auth.gatewayauth.AuthUIControlClickListener;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.mobile.auth.gatewayauth.ResultCode;
import com.mobile.auth.gatewayauth.TokenResultListener;
import com.mobile.auth.gatewayauth.model.TokenRet;
import com.sq.dlyz_flutter.DouluoManagerChannel;
import org.json.JSONObject;

/**
 * 闪验登录管理类 - 基于原始37SDK实现的简化版本
 */
public class FastLoginManager {

    private static final String TAG = "FastLogin";

    @SuppressLint("StaticFieldLeak")
    private static volatile FastLoginManager instance;

    private final Context mContext;

    private PhoneNumberAuthHelper mPhoneNumberAuthHelper;

    private String accessKey; // 真实的accessKey，由Flutter传入

    private String appKey; // 解密密钥，由Flutter传入

    private String userAgreementUrl; // 用户协议地址

    private String privacyPolicyUrl; // 隐私政策地址

    private FastLoginListener fastLoginListener;

    //是否请求过accessKey了
    private boolean requestedAccessKey = false;

    private FastLoginManager(Context context) {
        this.mContext = context;
    }

    public static FastLoginManager getInstance(Context context) {
        if (instance == null) {
            synchronized (FastLoginManager.class) {
                if (instance == null) {
                    instance = new FastLoginManager(context);
                }
            }
        }
        return instance;
    }


    /**
     * 设置解密密钥
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
    
    /**
     * 设置协议地址
     */
    public void setAgreementUrls(String userAgreementUrl, String privacyPolicyUrl) {
        this.userAgreementUrl = userAgreementUrl;
        this.privacyPolicyUrl = privacyPolicyUrl;
        Log.d(TAG, "设置协议地址完成");
    }

    /**
     * 获取用户协议地址
     */
    public String getUserAgreementUrl() {
        return userAgreementUrl;
    }

    /**
     * 获取隐私政策地址
     */
    public String getPrivacyPolicyUrl() {
        return privacyPolicyUrl;
    }
    
    /**
     * 设置accessKey
     */
    public void setAccessKey(String encryptedAccessKey) {
        this.accessKey = decodeAccessKey(encryptedAccessKey);
        this.requestedAccessKey = true;
        Log.d(TAG, "Access key set and decrypted: " + (accessKey != null ? accessKey: "null"));
        
        // 如果PhoneNumberAuthHelper已经初始化，立即设置access_key
        if (mPhoneNumberAuthHelper != null) {
            mPhoneNumberAuthHelper.setAuthSDKInfo(accessKey);
            Log.d(TAG, "Updated existing PhoneNumberAuthHelper with new access key");
        }
    }
    
    /**
     * 解密accessKey
     * 使用Flutter传入的appKey作为解密密钥对服务器返回的加密accessKey进行解密
     * 参考原版37SDK实现
     */
    private String decodeAccessKey(String encryptedAccessKey) {
        if (TextUtils.isEmpty(encryptedAccessKey)) {
            Log.e(TAG, "加密的access key为空");
            return null;
        }
        
        // 检查是否已设置appKey
        if (TextUtils.isEmpty(this.appKey)) {
            Log.e(TAG, "解密密钥(appKey)未设置，无法解密access key");
            return encryptedAccessKey; // 返回原始key，让上层处理
        }
        
        String decodeKey = "";
        Log.i(TAG, "encryptedAccessKey打印 " + encryptedAccessKey);
        
        int length = this.appKey.length();
        // 不足16位则补齐0
        if (length < 16) {
            StringBuilder sb = new StringBuilder(this.appKey);
            for (int i = 0; i < 16 - length; i++) {
                sb.append("0");
            }
            decodeKey = sb.toString();
        } else {
            // 满16位则截取前16位
            decodeKey = this.appKey.substring(0, 16);
        }
        
        Log.d(TAG, "解密密钥: " + decodeKey + ", 长度: " + decodeKey.length());
        String decodedKey = AESUtil.decryptString(encryptedAccessKey, decodeKey);
        
        if (TextUtils.isEmpty(decodedKey)) {
            Log.e(TAG, "Access key解密失败，使用原始key");
            return encryptedAccessKey; // 如果解密失败，使用原始key
        }
        
        Log.d(TAG, "Access key解密成功");
        return decodedKey;
    }

    /**
     * 执行闪验操作
     */
    public void doFastVerifyLogin(final FastLoginListener listener) {
        this.fastLoginListener = listener;
        
        // 检查accessKey是否已设置
        if (accessKey == null || accessKey.isEmpty()) {
            Log.e(TAG, "Access key未设置，无法执行闪验登录");
            disableFastLogin(FastLoginConstants.Code.FAILURE_CONFIG_ERROR, "Access key未设置");
            return;
        }
        
        if (requestedAccessKey()) {
            oneKeyLogin();
            return;
        }
        
        Log.d(TAG, "开始执行闪验登录，access key已配置");
        try {
            requestedAccessKey = true;
            oneKeyLogin();
        } catch (Exception e) {
            Log.e(TAG, "闪验配置异常", e);
            disableFastLogin(FastLoginConstants.Code.FAILURE_CONFIG_ERROR, FastLoginConstants.MESSAGE.FAILURE_CONFIG_ERROR);
        }
    }

    public void initFastAccessKey(final AuthResultCallback listener) {
        // 检查是否已经有有效的access key
        if (accessKey != null && !accessKey.isEmpty() && requestedAccessKey()) {
            Log.d(TAG, "Access key已配置，跳过初始化");
            listener.onSuccess();
            return;
        }
        
        // 检查access key是否已设置
        if (accessKey == null || accessKey.isEmpty()) {
            Log.e(TAG, "Access key未设置，初始化失败");
            listener.onFailure(0, "Access key未设置");
            return;
        }
        
        Log.d(TAG, "使用真实access key初始化闪验配置");
        try {
            requestedAccessKey = true;
            listener.onSuccess();
        } catch (Exception e) {
            Log.e(TAG, "闪验配置异常", e);
            listener.onFailure(0, "闪验配置异常");
        }
    }

    /**
     * 闪验不可用
     */
    private void disableFastLogin(String code, String msg) {
        Log.w(TAG, "禁用闪验登录, code=" + code + ", msg=" + msg);
        Bundle bundle = new Bundle();
        bundle.putString(FastLoginConstants.BundleKey.CODE, code);
        bundle.putString(FastLoginConstants.BundleKey.MESSAGE, msg);
        disableFastLogin(bundle);
    }

    /**
     * 闪验不可用
     */
    private void disableFastLogin(Bundle bundle) {
        if (fastLoginListener != null) {
            Log.w(TAG, "回调闪验失败");
            fastLoginListener.onFastLoginFail(bundle);
        }
    }

    /**
     * 一键登录
     */
    public void oneKeyLogin() {
        try {
            fastInit();
            fastLogin();
        } catch (Exception e) {
            e.printStackTrace();
            disableFastLogin(FastLoginConstants.Code.FAILURE_NOT_SUPPORT, FastLoginConstants.MESSAGE.FAILURE_NOT_SUPPORT);
        }
    }

    public boolean requestedAccessKey() {
        return requestedAccessKey;
    }

    /**
     * 初始化
     */
    private void fastInit() {
        Log.d(TAG, "初始化闪验");
        TokenResultListener tokenResultListener = new TokenResultListener() {
            @Override
            public void onTokenSuccess(String s) {
                TokenRet tokenRet = null;
                try {
                    tokenRet = TokenRet.fromJson(s);
                    if (ResultCode.CODE_START_AUTHPAGE_SUCCESS.equals(tokenRet.getCode())) {
                        Log.i(TAG, "唤起授权页成功: " + s);
                    } else if (ResultCode.CODE_SUCCESS.equals(tokenRet.getCode())) {
                        Log.i(TAG, "获取token成功: " + s);
                        mPhoneNumberAuthHelper.hideLoginLoading();
                        verifyFastToken(tokenRet.getToken());
                    }
                } catch (Exception e) {
                    Log.e(TAG, "解析token异常", e);
                    disableFastLogin(FastLoginConstants.Code.FAILURE_PARSE_ALI,
                        FastLoginConstants.MESSAGE.FAILURE_PARSE_ALI);
                }
            }

            @Override
            public void onTokenFailed(String s) {
                Log.w(TAG, "获取token失败: " + s);
                mPhoneNumberAuthHelper.hideLoginLoading();
                TokenRet tokenRet = null;
                try {
                    tokenRet = TokenRet.fromJson(s);
                    if (ResultCode.CODE_ERROR_USER_CANCEL.equals(tokenRet.getCode())) {
                        disableFastLogin(FastLoginConstants.Code.CANCEL, FastLoginConstants.MESSAGE.CANCEL);
                        if (fastLoginListener != null) {
                            fastLoginListener.onFastRelease();
                        }
                        quitLoginPage();
                    } else if ("600023".equals(tokenRet.getCode())) {
                        disableFastLogin(tokenRet.getCode(), tokenRet.getMsg());
                        if (fastLoginListener != null) {
                            fastLoginListener.onFastRelease();
                        }
                        quitLoginPage();
                    } else {
                        disableFastLogin(tokenRet.getCode(), tokenRet.getMsg());
                    }
                } catch (Exception e) {
                    Log.e(TAG, "解析token异常", e);
                    disableFastLogin(FastLoginConstants.Code.FAILURE_PARSE_ALI_FAILURE,
                        FastLoginConstants.MESSAGE.FAILURE_PARSE_ALI_FAILURE);
                }
            }
        };
        mPhoneNumberAuthHelper = PhoneNumberAuthHelper.getInstance(mContext, tokenResultListener);
        
        // 设置解密后的access key
        if (accessKey != null) {
            mPhoneNumberAuthHelper.setAuthSDKInfo(accessKey);
            Log.d(TAG, "设置解密后的access key到PhoneNumberAuthHelper");
        } else {
            Log.e(TAG, "Access key为空，无法设置到PhoneNumberAuthHelper");
        }
        
        try {
            // 获取DouluoManagerChannel实例以支持UI事件回调
            DouluoManagerChannel channel = DouluoManagerChannel.getInstance(mContext);
            // 传入channel和授权应用列表状态用于回调
            boolean hasAuthApps = channel.hasAuthApps();
            FastLoginUIConfig.configAuthPage(mPhoneNumberAuthHelper, mContext, channel, hasAuthApps);
            Log.d(TAG, "37SDK标准UI配置完成，已启用Flutter回调，hasAuthApps=" + hasAuthApps);
        } catch (Exception e) {
            Log.e(TAG, "配置37SDK标准UI失败，使用默认UI", e);
        }
        // ========================================================
        
        mPhoneNumberAuthHelper.setUIClickListener(new AuthUIControlClickListener() {
            @Override
            public void onClick(String code, Context context, String data) {
                Log.d(TAG, "授权页点击 code=" + code + ", data=" + data);
                handleClickBtn(code, context, data);
            }
        });
    }

    private void fastLogin() {
        Log.d(TAG, "拉起闪验授权页");
        //拉起授权页
        mPhoneNumberAuthHelper.getLoginToken(mContext, 5000);
    }

    public void getFastEnv(FastBooleanResultListener fastBooleanResultListener){
        Log.i(TAG, "检查闪验环境");
        
        // 检查access_key是否存在
        if (accessKey == null || accessKey.isEmpty()) {
            Log.e(TAG, "Access key为空，无法检查闪验环境");
            fastBooleanResultListener.callback(false);
            return;
        }
        
        initFastAccessKey(new AuthResultCallback() {
            @Override
            public void onSuccess() {
                boolean isFastEnv;
                // 检查闪验环境
                if(mPhoneNumberAuthHelper == null){
                    Log.e(TAG, "mPhoneNumberAuthHelper为空，初始化闪验");
                    fastInit();
                }
                
                // 确保access_key已设置
                if (accessKey != null) {
                    mPhoneNumberAuthHelper.setAuthSDKInfo(accessKey);
                    Log.d(TAG, "设置access key到PhoneNumberAuthHelper用于环境检查");
                }
                
                isFastEnv = mPhoneNumberAuthHelper.checkEnvAvailable();
                Log.i(TAG, "闪验环境检测结果：" + isFastEnv);
                fastBooleanResultListener.callback(isFastEnv);
            }

            @Override
            public void onFailure(int code, String msg) {
                Log.e(TAG, "闪验配置获取失败，闪验环境返回false");
                fastBooleanResultListener.callback(false);
            }
        });
    }

    /**
     * 处理授权页点击按钮事件
     *
     * @param code
     * @param context
     * @param data
     */
    private void handleClickBtn(String code, Context context, String data) {
        switch (code) {
            case "700002":
                //点击一键登录
                Log.d(TAG, "点击一键登录");
                try {
                    JSONObject object = new JSONObject(data);
                    boolean isChecked = object.optBoolean("isChecked");
                    if (!isChecked) {
                        Toast.makeText(mContext, "请勾选用户协议与隐私政策再登录", Toast.LENGTH_SHORT).show();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
            case "700003":
                //点击同意用户协议
                Log.d(TAG, "点击同意用户协议");
                break;
            default:
                break;
        }
    }

    /**
     * 拿到闪验的token后传递给Flutter端
     *
     * @param token
     */
    public void verifyFastToken(final String token) {
        Log.d(TAG, "获取到闪验token: " + token);
        
        // 直接将token传递给Flutter端，由Flutter调用后端接口进行验证
        try {
            Log.i(TAG, "闪验token获取成功，传递给Flutter端处理");
            if (fastLoginListener != null) {
                fastLoginListener.onFastLoginSuccess(token);
                fastLoginListener.onFastRelease();
            }
            quitLoginPage();
        } catch (Exception e) {
            Log.e(TAG, "传递token异常", e);
            disableFastLogin(FastLoginConstants.Code.FAILURE_VERIFY_FAIL, FastLoginConstants.MESSAGE.FAILURE_VERIFY_FAIL);
        }
    }

    public interface FastLoginListener {
        //验证失败
        void onFastLoginFail(Bundle bundle);

        //验证成功，传递token给Flutter端
        void onFastLoginSuccess(String token);

        //释放
        void onFastRelease();

        //验证账号后是否需要重新打开闪验弹窗
        void onVerifyAccount(boolean needOpen);
    }

    public void quitLoginPage() {
        Log.w(TAG, "退出登录页");
        if (mPhoneNumberAuthHelper != null) {
            mPhoneNumberAuthHelper.setAuthListener(null);
            if (fastLoginListener != null) {
                fastLoginListener.onFastRelease();
            }
            mPhoneNumberAuthHelper.quitLoginPage();
        }
    }
}