// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from <PERSON><PERSON> (v25.5.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

package io.flutter.plugins.videoplayer;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.CLASS;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.flutter.plugin.common.BasicMessageChannel;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MessageCodec;
import io.flutter.plugin.common.StandardMessageCodec;
import java.io.ByteArrayOutputStream;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;

/** Generated class from Pigeon. */
@SuppressWarnings({"unused", "unchecked", "CodeBlock2Expr", "RedundantSuppression", "serial"})
public class Messages {

  /** Error class for passing custom error details to Flutter via a thrown PlatformException. */
  public static class FlutterError extends RuntimeException {

    /** The error code. */
    public final String code;

    /** The error details. Must be a datatype supported by the api codec. */
    public final Object details;

    public FlutterError(@NonNull String code, @Nullable String message, @Nullable Object details) {
      super(message);
      this.code = code;
      this.details = details;
    }
  }

  @NonNull
  protected static ArrayList<Object> wrapError(@NonNull Throwable exception) {
    ArrayList<Object> errorList = new ArrayList<>(3);
    if (exception instanceof FlutterError) {
      FlutterError error = (FlutterError) exception;
      errorList.add(error.code);
      errorList.add(error.getMessage());
      errorList.add(error.details);
    } else {
      errorList.add(exception.toString());
      errorList.add(exception.getClass().getSimpleName());
      errorList.add(
          "Cause: " + exception.getCause() + ", Stacktrace: " + Log.getStackTraceString(exception));
    }
    return errorList;
  }

  @Target(METHOD)
  @Retention(CLASS)
  @interface CanIgnoreReturnValue {}

  /** Pigeon equivalent of VideoViewType. */
  public enum PlatformVideoViewType {
    TEXTURE_VIEW(0),
    PLATFORM_VIEW(1);

    final int index;

    PlatformVideoViewType(final int index) {
      this.index = index;
    }
  }

  /** Pigeon equivalent of video_platform_interface's VideoFormat. */
  public enum PlatformVideoFormat {
    DASH(0),
    HLS(1),
    SS(2);

    final int index;

    PlatformVideoFormat(final int index) {
      this.index = index;
    }
  }

  /**
   * Information passed to the platform view creation.
   *
   * <p>Generated class from Pigeon that represents data sent in messages.
   */
  public static final class PlatformVideoViewCreationParams {
    private @NonNull Long playerId;

    public @NonNull Long getPlayerId() {
      return playerId;
    }

    public void setPlayerId(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"playerId\" is null.");
      }
      this.playerId = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    PlatformVideoViewCreationParams() {}

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      PlatformVideoViewCreationParams that = (PlatformVideoViewCreationParams) o;
      return playerId.equals(that.playerId);
    }

    @Override
    public int hashCode() {
      return Objects.hash(playerId);
    }

    public static final class Builder {

      private @Nullable Long playerId;

      @CanIgnoreReturnValue
      public @NonNull Builder setPlayerId(@NonNull Long setterArg) {
        this.playerId = setterArg;
        return this;
      }

      public @NonNull PlatformVideoViewCreationParams build() {
        PlatformVideoViewCreationParams pigeonReturn = new PlatformVideoViewCreationParams();
        pigeonReturn.setPlayerId(playerId);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<>(1);
      toListResult.add(playerId);
      return toListResult;
    }

    static @NonNull PlatformVideoViewCreationParams fromList(
        @NonNull ArrayList<Object> pigeonVar_list) {
      PlatformVideoViewCreationParams pigeonResult = new PlatformVideoViewCreationParams();
      Object playerId = pigeonVar_list.get(0);
      pigeonResult.setPlayerId((Long) playerId);
      return pigeonResult;
    }
  }

  /** Generated class from Pigeon that represents data sent in messages. */
  public static final class CreateMessage {
    private @NonNull String uri;

    public @NonNull String getUri() {
      return uri;
    }

    public void setUri(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"uri\" is null.");
      }
      this.uri = setterArg;
    }

    private @Nullable PlatformVideoFormat formatHint;

    public @Nullable PlatformVideoFormat getFormatHint() {
      return formatHint;
    }

    public void setFormatHint(@Nullable PlatformVideoFormat setterArg) {
      this.formatHint = setterArg;
    }

    private @NonNull Map<String, String> httpHeaders;

    public @NonNull Map<String, String> getHttpHeaders() {
      return httpHeaders;
    }

    public void setHttpHeaders(@NonNull Map<String, String> setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"httpHeaders\" is null.");
      }
      this.httpHeaders = setterArg;
    }

    private @Nullable String userAgent;

    public @Nullable String getUserAgent() {
      return userAgent;
    }

    public void setUserAgent(@Nullable String setterArg) {
      this.userAgent = setterArg;
    }

    private @Nullable PlatformVideoViewType viewType;

    public @Nullable PlatformVideoViewType getViewType() {
      return viewType;
    }

    public void setViewType(@Nullable PlatformVideoViewType setterArg) {
      this.viewType = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    CreateMessage() {}

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      CreateMessage that = (CreateMessage) o;
      return uri.equals(that.uri)
          && Objects.equals(formatHint, that.formatHint)
          && httpHeaders.equals(that.httpHeaders)
          && Objects.equals(userAgent, that.userAgent)
          && Objects.equals(viewType, that.viewType);
    }

    @Override
    public int hashCode() {
      return Objects.hash(uri, formatHint, httpHeaders, userAgent, viewType);
    }

    public static final class Builder {

      private @Nullable String uri;

      @CanIgnoreReturnValue
      public @NonNull Builder setUri(@NonNull String setterArg) {
        this.uri = setterArg;
        return this;
      }

      private @Nullable PlatformVideoFormat formatHint;

      @CanIgnoreReturnValue
      public @NonNull Builder setFormatHint(@Nullable PlatformVideoFormat setterArg) {
        this.formatHint = setterArg;
        return this;
      }

      private @Nullable Map<String, String> httpHeaders;

      @CanIgnoreReturnValue
      public @NonNull Builder setHttpHeaders(@NonNull Map<String, String> setterArg) {
        this.httpHeaders = setterArg;
        return this;
      }

      private @Nullable String userAgent;

      @CanIgnoreReturnValue
      public @NonNull Builder setUserAgent(@Nullable String setterArg) {
        this.userAgent = setterArg;
        return this;
      }

      private @Nullable PlatformVideoViewType viewType;

      @CanIgnoreReturnValue
      public @NonNull Builder setViewType(@Nullable PlatformVideoViewType setterArg) {
        this.viewType = setterArg;
        return this;
      }

      public @NonNull CreateMessage build() {
        CreateMessage pigeonReturn = new CreateMessage();
        pigeonReturn.setUri(uri);
        pigeonReturn.setFormatHint(formatHint);
        pigeonReturn.setHttpHeaders(httpHeaders);
        pigeonReturn.setUserAgent(userAgent);
        pigeonReturn.setViewType(viewType);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<>(5);
      toListResult.add(uri);
      toListResult.add(formatHint);
      toListResult.add(httpHeaders);
      toListResult.add(userAgent);
      toListResult.add(viewType);
      return toListResult;
    }

    static @NonNull CreateMessage fromList(@NonNull ArrayList<Object> pigeonVar_list) {
      CreateMessage pigeonResult = new CreateMessage();
      Object uri = pigeonVar_list.get(0);
      pigeonResult.setUri((String) uri);
      Object formatHint = pigeonVar_list.get(1);
      pigeonResult.setFormatHint((PlatformVideoFormat) formatHint);
      Object httpHeaders = pigeonVar_list.get(2);
      pigeonResult.setHttpHeaders((Map<String, String>) httpHeaders);
      Object userAgent = pigeonVar_list.get(3);
      pigeonResult.setUserAgent((String) userAgent);
      Object viewType = pigeonVar_list.get(4);
      pigeonResult.setViewType((PlatformVideoViewType) viewType);
      return pigeonResult;
    }
  }

  private static class PigeonCodec extends StandardMessageCodec {
    public static final PigeonCodec INSTANCE = new PigeonCodec();

    private PigeonCodec() {}

    @Override
    protected Object readValueOfType(byte type, @NonNull ByteBuffer buffer) {
      switch (type) {
        case (byte) 129:
          {
            Object value = readValue(buffer);
            return value == null ? null : PlatformVideoViewType.values()[((Long) value).intValue()];
          }
        case (byte) 130:
          {
            Object value = readValue(buffer);
            return value == null ? null : PlatformVideoFormat.values()[((Long) value).intValue()];
          }
        case (byte) 131:
          return PlatformVideoViewCreationParams.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 132:
          return CreateMessage.fromList((ArrayList<Object>) readValue(buffer));
        default:
          return super.readValueOfType(type, buffer);
      }
    }

    @Override
    protected void writeValue(@NonNull ByteArrayOutputStream stream, Object value) {
      if (value instanceof PlatformVideoViewType) {
        stream.write(129);
        writeValue(stream, value == null ? null : ((PlatformVideoViewType) value).index);
      } else if (value instanceof PlatformVideoFormat) {
        stream.write(130);
        writeValue(stream, value == null ? null : ((PlatformVideoFormat) value).index);
      } else if (value instanceof PlatformVideoViewCreationParams) {
        stream.write(131);
        writeValue(stream, ((PlatformVideoViewCreationParams) value).toList());
      } else if (value instanceof CreateMessage) {
        stream.write(132);
        writeValue(stream, ((CreateMessage) value).toList());
      } else {
        super.writeValue(stream, value);
      }
    }
  }

  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface AndroidVideoPlayerApi {

    void initialize();

    @NonNull
    Long create(@NonNull CreateMessage msg);

    void dispose(@NonNull Long playerId);

    void setMixWithOthers(@NonNull Boolean mixWithOthers);

    @NonNull
    String getLookupKeyForAsset(@NonNull String asset, @Nullable String packageName);

    /** The codec used by AndroidVideoPlayerApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return PigeonCodec.INSTANCE;
    }
    /**
     * Sets up an instance of `AndroidVideoPlayerApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setUp(
        @NonNull BinaryMessenger binaryMessenger, @Nullable AndroidVideoPlayerApi api) {
      setUp(binaryMessenger, "", api);
    }

    static void setUp(
        @NonNull BinaryMessenger binaryMessenger,
        @NonNull String messageChannelSuffix,
        @Nullable AndroidVideoPlayerApi api) {
      messageChannelSuffix = messageChannelSuffix.isEmpty() ? "" : "." + messageChannelSuffix;
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.AndroidVideoPlayerApi.initialize"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                try {
                  api.initialize();
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.AndroidVideoPlayerApi.create"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                CreateMessage msgArg = (CreateMessage) args.get(0);
                try {
                  Long output = api.create(msgArg);
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.AndroidVideoPlayerApi.dispose"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Long playerIdArg = (Long) args.get(0);
                try {
                  api.dispose(playerIdArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.AndroidVideoPlayerApi.setMixWithOthers"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Boolean mixWithOthersArg = (Boolean) args.get(0);
                try {
                  api.setMixWithOthers(mixWithOthersArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.AndroidVideoPlayerApi.getLookupKeyForAsset"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String assetArg = (String) args.get(0);
                String packageNameArg = (String) args.get(1);
                try {
                  String output = api.getLookupKeyForAsset(assetArg, packageNameArg);
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
  /** Generated interface from Pigeon that represents a handler of messages from Flutter. */
  public interface VideoPlayerInstanceApi {

    void setLooping(@NonNull Boolean looping);

    void setVolume(@NonNull Double volume);

    void setPlaybackSpeed(@NonNull Double speed);

    void play();

    @NonNull
    Long getPosition();

    void seekTo(@NonNull Long position);

    void pause();

    /** The codec used by VideoPlayerInstanceApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return PigeonCodec.INSTANCE;
    }
    /**
     * Sets up an instance of `VideoPlayerInstanceApi` to handle messages through the
     * `binaryMessenger`.
     */
    static void setUp(
        @NonNull BinaryMessenger binaryMessenger, @Nullable VideoPlayerInstanceApi api) {
      setUp(binaryMessenger, "", api);
    }

    static void setUp(
        @NonNull BinaryMessenger binaryMessenger,
        @NonNull String messageChannelSuffix,
        @Nullable VideoPlayerInstanceApi api) {
      messageChannelSuffix = messageChannelSuffix.isEmpty() ? "" : "." + messageChannelSuffix;
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.VideoPlayerInstanceApi.setLooping"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Boolean loopingArg = (Boolean) args.get(0);
                try {
                  api.setLooping(loopingArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.VideoPlayerInstanceApi.setVolume"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Double volumeArg = (Double) args.get(0);
                try {
                  api.setVolume(volumeArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.VideoPlayerInstanceApi.setPlaybackSpeed"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Double speedArg = (Double) args.get(0);
                try {
                  api.setPlaybackSpeed(speedArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.VideoPlayerInstanceApi.play"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                try {
                  api.play();
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.VideoPlayerInstanceApi.getPosition"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                try {
                  Long output = api.getPosition();
                  wrapped.add(0, output);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.VideoPlayerInstanceApi.seekTo"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                Long positionArg = (Long) args.get(0);
                try {
                  api.seekTo(positionArg);
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger,
                "dev.flutter.pigeon.video_player_android.VideoPlayerInstanceApi.pause"
                    + messageChannelSuffix,
                getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<>();
                try {
                  api.pause();
                  wrapped.add(0, null);
                } catch (Throwable exception) {
                  wrapped = wrapError(exception);
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
}
